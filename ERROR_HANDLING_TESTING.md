# Тестування системи обробки помилок

## Як перевірити покращення обробки помилок

### 1. Тестування мережевих помилок (Network Error)

#### Сценарій: API сервер недоступний

**Кроки для тестування:**
1. Зупиніть API сервер або відключіть інтернет
2. Відкрийте додаток у браузері
3. Перейдіть на сторінку завантаження банківських файлів (`/banks`)
4. Спробуйте завантажити файл

**Очікуваний результат:**
- ❌ **Раніше**: Показувався страшний трейс помилки з `AxiosError: Network Error`
- ✅ **Тепер**: Показується зрозуміле повідомлення: "Сервер недоступний. Перевірте підключення до інтернету або спробуйте пізніше."

#### Сценарій: Завантаження даних платежів

**Кроки для тестування:**
1. Зупиніть API сервер
2. Перейдіть на сторінку платежів за рік/місяць
3. Спробуйте змінити фільтри (рік, місяць, валюту)

**Очікуваний результат:**
- ❌ **Раніше**: Технічні помилки в консолі, дані не завантажуються
- ✅ **Тепер**: Зрозуміле повідомлення про недоступність сервера, дані очищуються

### 2. Тестування помилок авторизації

#### Сценарій: Протухший токен

**Кроки для тестування:**
1. Увійдіть в систему
2. В Developer Tools видаліть або пошкодьте токен в localStorage
3. Спробуйте виконати будь-яку дію, що потребує авторизації

**Очікуваний результат:**
- ✅ Показується повідомлення: "Сесія закінчилася. Будь ласка, увійдіть в систему знову."
- ✅ Автоматичне перенаправлення на сторінку входу

### 3. Тестування помилок сервера

#### Сценарій: Помилка 500

**Кроки для тестування:**
1. Налаштуйте API сервер для повернення помилки 500
2. Спробуйте виконати будь-який запит

**Очікуваний результат:**
- ✅ Показується повідомлення: "Помилка сервера. Спробуйте пізніше або зверніться до адміністратора."

### 4. Перевірка логування для розробників

**Кроки для тестування:**
1. Відкрийте Developer Tools (F12)
2. Перейдіть на вкладку Console
3. Викличте будь-яку помилку (наприклад, відключіть сервер і спробуйте завантажити дані)

**Очікуваний результат в консолі:**
```
🚨 Помилка в [Context]
  Повідомлення для користувача: [User-friendly message]
  Технічні деталі: [Technical error details]
  Статус: [HTTP status if available]
  Дані відповіді: [Response data if available]
```

### 5. Тестування різних компонентів

#### BankForm.vue
- Завантаження файлів при недоступному сервері
- Завантаження файлів з помилками валідації

#### PmtYearMon.vue
- Завантаження даних платежів за період
- Зміна фільтрів при недоступному сервері

#### Pmt.vue
- Завантаження списку років платежів
- Зміна валюти при недоступному сервері

### 6. Автоматичні тести

Запустіть тести для перевірки логіки обробки помилок:

```bash
npm run test:unit src/utils/__tests__/errorHandler.test.ts
```

### 7. Перевірка retry логіки

**Кроки для тестування:**
1. Налаштуйте API сервер для тимчасової недоступності
2. Виконайте запит
3. Перевірте в Network tab Developer Tools, що мережеві помилки не повторюються

**Очікуваний результат:**
- ✅ Мережеві помилки (Network Error) не повторюються
- ✅ Помилки авторизації (401/403) не повторюються
- ✅ Інші помилки повторюються максимум 1 раз

## Порівняння до і після

### До впровадження
```
Uncaught runtime errors:
×
ERROR
Network Error
AxiosError: Network Error
    at XMLHttpRequest.handleError (webpack-internal:///./node_modules/axios/lib/adapters/xhr.js:112:14)
    at Axios.request (webpack-internal:///./node_modules/axios/lib/core/Axios.js:54:41)
    at async Proxy.safeApiRequest (webpack-internal:///./node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/@vue/cli-service/node_modules/vue-loader/dist/index.js??ruleSet[0].use[0]!./src/components/Payments/PmtYearMon.vue?vue&type=script&lang=js:72:26)
```

### Після впровадження
```
[Зрозуміле повідомлення в UI]: "Сервер недоступний. Перевірте підключення до інтернету або спробуйте пізніше."

[В консолі для розробників]:
🚨 Помилка в PmtYearMon API request
  Повідомлення для користувача: Сервер недоступний. Перевірте підключення до інтернету або спробуйте пізніше.
  Технічні деталі: AxiosError: Network Error
```

## Переваги нової системи

1. **Користувачі** бачать зрозумілі повідомлення замість технічних трейсів
2. **Розробники** отримують детальну інформацію для діагностики
3. **Централізована** обробка всіх типів помилок
4. **Типізована** система з TypeScript
5. **Гнучка** архітектура для додавання нових типів помилок

## Налаштування для різних середовищ

### Development
- Детальне логування в консоль
- Показ технічних деталей для розробників

### Production
- Тільки зрозумілі повідомлення для користувачів
- Мінімальне логування (можна налаштувати через змінні середовища)
