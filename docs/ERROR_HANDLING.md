# Система обробки помилок

## Огляд

Реалізована централізована система обробки помилок, яка перетворює технічні помилки API на зрозумілі повідомлення для користувачів.

## Основні компоненти

### 1. Утиліта errorHandler.ts

Розташування: `src/utils/errorHandler.ts`

Основні функції:
- `getErrorMessage(error, defaultMessage)` - отримує зрозуміле повідомлення про помилку
- `logError(error, context)` - логує помилку з контекстом для розробки
- `isNetworkError(error)` - перевіряє, чи є помилка мережевою
- `isAuthError(error)` - перевіряє, чи є помилка авторизації
- `isServerError(error)` - перевіряє, чи є помилка сервера (5xx)

### 2. Типи помилок та повідомлення

#### Мережеві помилки (Network Error)
- **Технічна помилка**: `AxiosError: Network Error`
- **Повідомлення користувачу**: "Сервер недоступний. Перевірте підключення до інтернету або спробуйте пізніше."

#### Помилки авторизації (401/403)
- **401**: "Сесія закінчилася. Будь ласка, увійдіть в систему знову."
- **403**: "У вас немає прав для виконання цієї дії."

#### Помилки сервера (5xx)
- **Повідомлення**: "Помилка сервера. Спробуйте пізніше або зверніться до адміністратора."

#### Timeout помилки
- **Повідомлення**: "Час очікування відповіді від сервера вичерпано. Спробуйте ще раз."

## Інтеграція в компонентах

### Приклад використання в Vue компоненті

```typescript
import { getErrorMessage, logError } from '@/utils/errorHandler';

// В обробнику помилок
onError: (error: AxiosError) => {
  logError(error, "Component name - action description");
  const errorMessage = getErrorMessage(error, "Повідомлення за замовчуванням");
  if (this.$refs.myAlert) {
    this.$refs.myAlert.showAlert("danger", errorMessage);
  }
}
```

### Оновлені компоненти

1. **BankForm.vue** - обробка помилок завантаження файлів
2. **PmtYearMon.vue** - обробка помилок завантаження даних платежів
3. **Pmt.vue** - обробка помилок завантаження років платежів

## Глобальні обробники

### 1. Vue Error Handler (main.ts)
Перехоплює всі необроблені помилки Vue компонентів.

### 2. Unhandled Promise Rejection Handler (main.ts)
Перехоплює необроблені відхилення промісів.

### 3. Axios Interceptors (axios-config.ts)
Обробляють помилки HTTP запитів на рівні Axios.

### 4. TanStack Query Error Handlers (query-client.ts)
Обробляють помилки запитів та мутацій TanStack Query.

## Логування для розробки

Всі помилки логуються з детальною інформацією для розробників:

```javascript
console.group(`🚨 Помилка в ${context}`);
console.log("Повідомлення для користувача:", userMessage);
console.log("Технічні деталі:", error);
if (error.response) {
  console.log("Статус:", error.response.status);
  console.log("Дані відповіді:", error.response.data);
}
console.groupEnd();
```

## Retry логіка

Система автоматично не повторює:
- Мережеві помилки (Network Error)
- Помилки авторизації (401/403)
- Помилки сервера (5xx)

Інші помилки повторюються максимум 1 раз.

## Переваги нової системи

1. **Зрозумілі повідомлення** - користувачі бачать зрозумілі повідомлення замість технічних трейсів
2. **Централізована обробка** - всі помилки обробляються однаково
3. **Детальне логування** - розробники отримують всю необхідну інформацію для діагностики
4. **Типізація** - TypeScript забезпечує безпеку типів
5. **Гнучкість** - легко додавати нові типи помилок та повідомлення

## Додавання нових типів помилок

Для додавання нового типу помилки:

1. Оновіть функцію `getErrorMessage` в `errorHandler.ts`
2. Додайте відповідну перевірку (наприклад, `isCustomError`)
3. Оновіть документацію

## Тестування

Для тестування системи обробки помилок:

1. Відключіть сервер API
2. Спробуйте завантажити файл в BankForm
3. Перевірте, що показується зрозуміле повідомлення замість технічного трейсу
4. Перевірте консоль розробника на наявність детальної інформації про помилку
