import { getErrorMessage, logError, isNetworkError, isAuthError, isServerError } from '../errorHandler';
import type { AxiosError } from 'axios';

// Mock console methods
const originalConsole = { ...console };
beforeEach(() => {
  console.group = jest.fn();
  console.groupEnd = jest.fn();
  console.log = jest.fn();
  console.warn = jest.fn();
});

afterEach(() => {
  Object.assign(console, originalConsole);
});

describe('errorHandler', () => {
  describe('getErrorMessage', () => {
    it('should return network error message for Network Error', () => {
      const error = {
        code: 'ERR_NETWORK',
        message: 'Network Error'
      } as AxiosError;

      const result = getErrorMessage(error);
      expect(result).toBe("Сервер недоступний. Перевірте підключення до інтернету або спробуйте пізніше.");
    });

    it('should return timeout error message for timeout errors', () => {
      const error = {
        code: 'ECONNABORTED',
        message: 'timeout of 5000ms exceeded'
      } as AxiosError;

      const result = getErrorMessage(error);
      expect(result).toBe("Час очікування відповіді від сервера вичерпано. Спробуйте ще раз.");
    });

    it('should return auth error message for 401 status', () => {
      const error = {
        response: {
          status: 401,
          data: {}
        }
      } as AxiosError;

      const result = getErrorMessage(error);
      expect(result).toBe("Сесія закінчилася. Будь ласка, увійдіть в систему знову.");
    });

    it('should return forbidden error message for 403 status', () => {
      const error = {
        response: {
          status: 403,
          data: {}
        }
      } as AxiosError;

      const result = getErrorMessage(error);
      expect(result).toBe("У вас немає прав для виконання цієї дії.");
    });

    it('should return server error message for 5xx status', () => {
      const error = {
        response: {
          status: 500,
          data: {}
        }
      } as AxiosError;

      const result = getErrorMessage(error);
      expect(result).toBe("Помилка сервера. Спробуйте пізніше або зверніться до адміністратора.");
    });

    it('should return detail string from response data', () => {
      const error = {
        response: {
          status: 400,
          data: {
            detail: "Custom error message"
          }
        }
      } as AxiosError;

      const result = getErrorMessage(error);
      expect(result).toBe("Custom error message");
    });

    it('should return validation error message from detail array', () => {
      const error = {
        response: {
          status: 422,
          data: {
            detail: [
              {
                msg: "Validation failed",
                type: "value_error",
                loc: ["field"]
              }
            ]
          }
        }
      } as AxiosError;

      const result = getErrorMessage(error);
      expect(result).toBe("Validation failed");
    });

    it('should return default message when no specific error found', () => {
      const error = {
        response: {
          status: 400,
          data: {}
        }
      } as AxiosError;

      const result = getErrorMessage(error, "Custom default");
      expect(result).toBe("Custom default");
    });

    it('should handle error without response', () => {
      const error = {
        request: {},
        message: "Request failed"
      } as AxiosError;

      const result = getErrorMessage(error);
      expect(result).toBe("Не вдалося з'єднатися з сервером. Перевірте підключення до інтернету.");
    });
  });

  describe('isNetworkError', () => {
    it('should return true for ERR_NETWORK code', () => {
      const error = { code: 'ERR_NETWORK' } as AxiosError;
      expect(isNetworkError(error)).toBe(true);
    });

    it('should return true for Network Error message', () => {
      const error = { message: 'Network Error' } as AxiosError;
      expect(isNetworkError(error)).toBe(true);
    });

    it('should return true when no response', () => {
      const error = { message: 'Some error' } as AxiosError;
      expect(isNetworkError(error)).toBe(true);
    });

    it('should return false for normal errors with response', () => {
      const error = { 
        response: { status: 400 },
        message: 'Bad request'
      } as AxiosError;
      expect(isNetworkError(error)).toBe(false);
    });
  });

  describe('isAuthError', () => {
    it('should return true for 401 status', () => {
      const error = { response: { status: 401 } } as AxiosError;
      expect(isAuthError(error)).toBe(true);
    });

    it('should return true for 403 status', () => {
      const error = { response: { status: 403 } } as AxiosError;
      expect(isAuthError(error)).toBe(true);
    });

    it('should return false for other statuses', () => {
      const error = { response: { status: 400 } } as AxiosError;
      expect(isAuthError(error)).toBe(false);
    });
  });

  describe('isServerError', () => {
    it('should return true for 500 status', () => {
      const error = { response: { status: 500 } } as AxiosError;
      expect(isServerError(error)).toBe(true);
    });

    it('should return true for 502 status', () => {
      const error = { response: { status: 502 } } as AxiosError;
      expect(isServerError(error)).toBe(true);
    });

    it('should return false for 4xx statuses', () => {
      const error = { response: { status: 400 } } as AxiosError;
      expect(isServerError(error)).toBe(false);
    });

    it('should return false when no response', () => {
      const error = { message: 'Network Error' } as AxiosError;
      expect(isServerError(error)).toBe(false);
    });
  });

  describe('logError', () => {
    it('should log error with context', () => {
      const error = { message: 'Test error' };
      const context = 'Test context';

      logError(error, context);

      expect(console.group).toHaveBeenCalledWith(`🚨 Помилка в ${context}`);
      expect(console.log).toHaveBeenCalledWith("Повідомлення для користувача:", expect.any(String));
      expect(console.log).toHaveBeenCalledWith("Технічні деталі:", error);
      expect(console.groupEnd).toHaveBeenCalled();
    });
  });
});
